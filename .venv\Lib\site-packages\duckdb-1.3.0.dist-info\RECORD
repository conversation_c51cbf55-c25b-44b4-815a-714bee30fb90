adbc_driver_duckdb/__init__.py,sha256=hHAwFLPujXrsm024U7AVua8bftOC4yNgc2GGdkXNrLI,1674
adbc_driver_duckdb/dbapi.py,sha256=NaanPiS8P8AE_sjd9vhKKSaIG3aTB2MWo_sTNHQVbjM,3578
duckdb-1.3.0.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
duckdb-1.3.0.dist-info/METADATA,sha256=7vxEAJ3zU72_o4_5z20FG3-SSgYJkhd7n4ida0mDXVc,7201
duckdb-1.3.0.dist-info/RECORD,,
duckdb-1.3.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
duckdb-1.3.0.dist-info/WHEEL,sha256=w9Xlqjb4P__1xPosTTDG8Ieqhmo6FNdeoRaV5xSg6-o,101
duckdb-1.3.0.dist-info/top_level.txt,sha256=PJ5o847brKVXDprOLYKxfLtb6iH7NVZtotg7NxIkG-4,39
duckdb-stubs/__init__.pyi,sha256=MwMVIHYWmNCiAZloDs5YrKNnfisdtSlErSHPB3L4-g0,48050
duckdb-stubs/functional/__init__.pyi,sha256=xJ3b7MdZpnU3NiCpN8WYudu4pHRE7YUPYTcc2TSR9hg,842
duckdb-stubs/typing/__init__.pyi,sha256=VHseLxfiq-RrlX881MGW1NYV3TsG2q0VmDGtbSx-8og,984
duckdb-stubs/value/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
duckdb-stubs/value/constant/__init__.pyi,sha256=ITxzXcoDUMwTpgblD6TP5njqnsa2GYfPrVD2e4qhBvQ,3426
duckdb/__init__.py,sha256=pWEXLC5VU_vDDOxJIRpdX27LZRXPBWVauNOIXolelxc,7755
duckdb/bytes_io_wrapper.py,sha256=o5WcKIHA6EoZTsssRNeoohLeGtCPKY-8yQw-iP1k0SY,3050
duckdb/duckdb.cp311-win_amd64.pyd,sha256=PJv1YlC_KjrdvE9X1TYXFXc0hqudNKjSMWz8y7TGiOQ,31459328
duckdb/experimental/__init__.py,sha256=JTAdXBrgCCenIGe3vzm7a3BuE1jOmgd9yMHG1ZEFSQA,46
duckdb/experimental/spark/LICENSE,sha256=vCGc3GQQzJKXFfRiLOJCSviQBJsHHzTWjewZVVVL8zI,13646
duckdb/experimental/spark/__init__.py,sha256=BKd5s6LUBabQjxliNhs4lAZx48a81Ch5E2NHS-udU3A,291
duckdb/experimental/spark/_globals.py,sha256=cYR8qr7E5fMAp7vsM8CjpwoLI4-_tnuhRS4qf7b0Vr8,2511
duckdb/experimental/spark/_typing.py,sha256=D4Q7AvSyVgNxnmJc3nYKgt3S7cgoRbf-YWo-8Mu54jI,1573
duckdb/experimental/spark/conf.py,sha256=N3PqcMyyNlgJQdjLKTVA_UH_isp7657tcvurp9CU0DE,1443
duckdb/experimental/spark/context.py,sha256=_1A8A7mlF1QSdeQ0XjPv2ImhN4ajziW7lxo2L4Ul3Ao,6373
duckdb/experimental/spark/errors/__init__.py,sha256=JikuqdfaCy_Nti4A_yXYk-z1m5sXab83rovIXjKfoH4,2218
duckdb/experimental/spark/errors/error_classes.py,sha256=5xPdevsInQCcA8NI7Hytzh1mrmX5WvHxe4jUKzocwUc,28167
duckdb/experimental/spark/errors/exceptions/__init__.py,sha256=skcDKqKaQuFEltWKiUwvSpw1Kzelup5daCFKByxT-Gk,800
duckdb/experimental/spark/errors/exceptions/base.py,sha256=p3Pp5GQIWLBK-ApWWGOCQJ1YA0LgWgZ9_N94O08Rj-U,5577
duckdb/experimental/spark/errors/utils.py,sha256=EnzRDKIciMDORkbZKWrn5KJuZkKtBSCpX9ok7qmRfEo,4554
duckdb/experimental/spark/exception.py,sha256=r3hWeu2EZ9ri0PAyTw38aD7G5qKVsOi8YgtgXMiGfts,550
duckdb/experimental/spark/sql/__init__.py,sha256=e_-3pLvS6PteoCxxCRgQHD_Mpd0JBXpbiNXTU47yjQ4,263
duckdb/experimental/spark/sql/_typing.py,sha256=FyJ1vvx5et2yH_YatPaLtI4Z40BK7Xwdtvw-GV13bTw,2454
duckdb/experimental/spark/sql/catalog.py,sha256=_8bruV2yeUk0HRhnY9lsyGRyXPLW1gtODU3V4UfIYJI,2362
duckdb/experimental/spark/sql/column.py,sha256=SJZWSZZTohHw7wTrS9DjpuzmPtid5Cee78I0o3Vi5hY,11076
duckdb/experimental/spark/sql/conf.py,sha256=eB1W0-wUa_fNJ0AAFMNxIGmhqr9a0IhPyr9Xesy-pes,679
duckdb/experimental/spark/sql/dataframe.py,sha256=lWONADE-fWfLVuHsndF2vBbjwtG7qC0QyJTssZZxMGE,47017
duckdb/experimental/spark/sql/functions.py,sha256=kImXKi_Jf3bsyeoqdGgkljPNIGLLllUIIFjwMtOA-aI,175126
duckdb/experimental/spark/sql/group.py,sha256=DLX16aSVFMV6xRvVJ1KCh7um09qTYJb3jQv_FQtTvy4,13695
duckdb/experimental/spark/sql/readwriter.py,sha256=ie5ENJX9Mxy6eBP9oNS-t5KMEQ3pfHfPvEdXDrVWGt0,17750
duckdb/experimental/spark/sql/session.py,sha256=XxzgYp78ZrDHyUcH42buJmODQo21-61YWF5hI3fRwsU,9290
duckdb/experimental/spark/sql/streaming.py,sha256=B0TyLDqG79fLU6Fz7x6TxmcoY5_o0bxziT1k479uMB8,1060
duckdb/experimental/spark/sql/type_utils.py,sha256=ks6YA2-4JUOHv3xrN3O6nT0d76CKVVxS0qNSS028uBE,3059
duckdb/experimental/spark/sql/types.py,sha256=Qqx9R65HA-WUT9MlGzy9b25RqfVdn_BW_ms6_DNRBmA,40434
duckdb/experimental/spark/sql/udf.py,sha256=hwWIvO0o3ipCGtmx9aUtpyC10PQ3NyQL102QB3Q1VqU,1118
duckdb/filesystem.py,sha256=WMVzCQ2gbe9T60hMw7aAajVqUOHRJvG0XvfgMkKi6hg,1019
duckdb/functional/__init__.py,sha256=at68LoHNhVX5kSKjEIdbz-joKO0ELUszIvwp95cqZ7A,235
duckdb/query_graph/__main__.py,sha256=BWDGf2LKiSXHhQWbIuvc-IXTjE_DqYISGvmtSBBYcx0,11622
duckdb/typing/__init__.py,sha256=OkjQrgmqY046kGMf2IdcSXawL9GjjY8acNNxpbq41Fc,921
duckdb/udf.py,sha256=-Y1DTF3uZgvufqO7ihvWXbFqWHu8LSME48NLyOeQKEk,693
duckdb/value/constant.py,sha256=cbTGO63hXI7tkoa6Sjw2S_PJYV6IAC8R88n2yVzPuNo,5784
